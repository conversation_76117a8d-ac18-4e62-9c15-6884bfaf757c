[gd_scene load_steps=23 format=3 uid="uid://csgcu4bgn35bt"]

[ext_resource type="Texture2D" uid="uid://cjf2jfhbvxagx" path="res://resources/solaria/UI/MainMenuSelectSave.png" id="1_k8woc"]
[ext_resource type="Script" uid="uid://swqfa833ew5d" path="res://scenes/MainManu/MainMenu.cs" id="1_mainmenu_script"]
[ext_resource type="Texture2D" uid="uid://dq4yqlwic411k" path="res://resources/solaria/UI/plusIcon.png" id="2_8wjrw"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="3_eu66t"]
[ext_resource type="Texture2D" uid="uid://beywyaey04k76" path="res://resources/solaria/UI/icon_yes.png" id="4_qlhc3"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="5_d85hk"]
[ext_resource type="Texture2D" uid="uid://d2rd6ldubw044" path="res://resources/solaria/UI/time_icon.png" id="5_eu66t"]

[sub_resource type="Animation" id="Animation_d85hk"]
resource_name = "CloseLevelSelect"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectSavePanel:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0.2),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectSavePanel:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.06, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(0.8, 0.8)]
}

[sub_resource type="Animation" id="Animation_eu66t"]
resource_name = "OpenLevelSelect"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectSavePanel:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectSavePanel:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.12143, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.8, 0.8), Vector2(1.2, 1.2), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_qlhc3"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectSavePanel:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectSavePanel:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_d85hk"]
_data = {
&"CloseLevelSelect": SubResource("Animation_d85hk"),
&"OpenLevelSelect": SubResource("Animation_eu66t"),
&"RESET": SubResource("Animation_qlhc3")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_qlhc3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_d85hk"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_t7gp2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_v4hxe"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gqn1c"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3ddtc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_w1fex"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2lrnb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gi81g"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sar08"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_uy1vm"]

[node name="MainMenu" type="Node2D"]
script = ExtResource("1_mainmenu_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
root_node = NodePath("../CanvasLayer/Control")
libraries = {
&"": SubResource("AnimationLibrary_d85hk")
}

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Buttons" type="Node2D" parent="CanvasLayer/Control"]

[node name="PlayButton" type="Button" parent="CanvasLayer/Control/Buttons"]
offset_left = -40.0
offset_right = 41.0
offset_bottom = 8.0
text = "Play	"

[node name="QuitButton" type="Button" parent="CanvasLayer/Control/Buttons"]
offset_left = -40.0
offset_top = 37.005
offset_right = 41.0
offset_bottom = 68.005
text = "Quit"

[node name="SelectSavePanel" type="Sprite2D" parent="CanvasLayer/Control"]
position = Vector2(0, 12)
texture = ExtResource("1_k8woc")

[node name="SaveSelect" type="Node2D" parent="CanvasLayer/Control/SelectSavePanel"]

[node name="Save1" type="Node2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect"]

[node name="NewSaveIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1"]
visible = false
position = Vector2(-143, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("2_8wjrw")

[node name="NewSaveLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1" instance=ExtResource("3_eu66t")]
visible = false
offset_left = -181.0
offset_top = -44.0
offset_right = -107.0
offset_bottom = -6.0
text = "NEW_SAVE"

[node name="LevelLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1" instance=ExtResource("3_eu66t")]
offset_left = -181.0
offset_top = -45.0
offset_right = -107.0
offset_bottom = -27.0
text = "LVL 100"

[node name="TimeLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1" instance=ExtResource("3_eu66t")]
offset_left = -157.0
offset_top = -27.0
offset_right = -107.0
offset_bottom = -9.0
text = "120:23"
horizontal_alignment = 0

[node name="YesIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1"]
position = Vector2(-143, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("4_qlhc3")

[node name="TimeIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1"]
position = Vector2(-166, -19)
texture = ExtResource("5_eu66t")

[node name="SelectButton" type="Button" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save1"]
offset_left = -184.0
offset_top = -48.0
offset_right = -104.0
offset_bottom = 32.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_qlhc3")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_d85hk")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_t7gp2")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_v4hxe")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_gqn1c")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_3ddtc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_w1fex")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_2lrnb")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gi81g")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_sar08")
theme_override_styles/normal = SubResource("StyleBoxEmpty_uy1vm")

[node name="Save2" type="Node2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect"]

[node name="NewSaveIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2"]
visible = false
position = Vector2(-143, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("2_8wjrw")

[node name="NewSaveLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2" instance=ExtResource("3_eu66t")]
visible = false
offset_left = -181.0
offset_top = -44.0
offset_right = -107.0
offset_bottom = -6.0
text = "NEW_SAVE"

[node name="LevelLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2" instance=ExtResource("3_eu66t")]
offset_left = -85.0
offset_top = -45.0
offset_right = -11.0
offset_bottom = -27.0
text = "LVL 100"

[node name="TimeLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2" instance=ExtResource("3_eu66t")]
offset_left = -61.0
offset_top = -27.0
offset_right = -11.0
offset_bottom = -9.0
text = "120:23"
horizontal_alignment = 0

[node name="YesIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2"]
position = Vector2(-47, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("4_qlhc3")

[node name="TimeIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2"]
position = Vector2(-70, -19)
texture = ExtResource("5_eu66t")

[node name="SelectButton" type="Button" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save2"]
offset_left = -88.0
offset_top = -48.0
offset_right = -8.0
offset_bottom = 32.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_qlhc3")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_d85hk")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_t7gp2")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_v4hxe")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_gqn1c")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_3ddtc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_w1fex")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_2lrnb")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gi81g")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_sar08")
theme_override_styles/normal = SubResource("StyleBoxEmpty_uy1vm")

[node name="Save3" type="Node2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect"]

[node name="NewSaveIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3"]
visible = false
position = Vector2(-143, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("2_8wjrw")

[node name="NewSaveLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3" instance=ExtResource("3_eu66t")]
visible = false
offset_left = -181.0
offset_top = -44.0
offset_right = -107.0
offset_bottom = -6.0
text = "NEW_SAVE"

[node name="LevelLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3" instance=ExtResource("3_eu66t")]
offset_left = 11.0
offset_top = -45.0
offset_right = 85.0
offset_bottom = -27.0
text = "LVL 100"

[node name="TimeLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3" instance=ExtResource("3_eu66t")]
offset_left = 35.0
offset_top = -27.0
offset_right = 85.0
offset_bottom = -9.0
text = "120:23"
horizontal_alignment = 0

[node name="YesIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3"]
position = Vector2(49, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("4_qlhc3")

[node name="TimeIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3"]
position = Vector2(26, -19)
texture = ExtResource("5_eu66t")

[node name="SelectButton" type="Button" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save3"]
offset_left = 8.0
offset_top = -48.0
offset_right = 88.0
offset_bottom = 32.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_qlhc3")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_d85hk")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_t7gp2")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_v4hxe")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_gqn1c")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_3ddtc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_w1fex")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_2lrnb")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gi81g")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_sar08")
theme_override_styles/normal = SubResource("StyleBoxEmpty_uy1vm")

[node name="Save4" type="Node2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect"]

[node name="NewSaveIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4"]
visible = false
position = Vector2(-143, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("2_8wjrw")

[node name="NewSaveLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4" instance=ExtResource("3_eu66t")]
visible = false
offset_left = -181.0
offset_top = -44.0
offset_right = -107.0
offset_bottom = -6.0
text = "NEW_SAVE"

[node name="LevelLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4" instance=ExtResource("3_eu66t")]
offset_left = 107.0
offset_top = -45.0
offset_right = 181.0
offset_bottom = -27.0
text = "LVL 100"

[node name="TimeLabel" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4" instance=ExtResource("3_eu66t")]
offset_left = 131.0
offset_top = -27.0
offset_right = 181.0
offset_bottom = -9.0
text = "120:23"
horizontal_alignment = 0

[node name="YesIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4"]
position = Vector2(145, 8)
scale = Vector2(2.17, 2.17)
texture = ExtResource("4_qlhc3")

[node name="TimeIcon" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4"]
position = Vector2(122, -19)
texture = ExtResource("5_eu66t")

[node name="SelectButton" type="Button" parent="CanvasLayer/Control/SelectSavePanel/SaveSelect/Save4"]
offset_left = 104.0
offset_top = -48.0
offset_right = 184.0
offset_bottom = 32.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_qlhc3")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_d85hk")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_t7gp2")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_v4hxe")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_gqn1c")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_3ddtc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_w1fex")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_2lrnb")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gi81g")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_sar08")
theme_override_styles/normal = SubResource("StyleBoxEmpty_uy1vm")

[node name="Close" type="Sprite2D" parent="CanvasLayer/Control/SelectSavePanel"]
position = Vector2(204, -68)
texture = ExtResource("5_d85hk")

[node name="CloseButton" type="Button" parent="CanvasLayer/Control/SelectSavePanel"]
offset_left = 194.0
offset_top = -80.0
offset_right = 214.0
offset_bottom = -58.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_qlhc3")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_d85hk")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_t7gp2")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_v4hxe")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_gqn1c")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_3ddtc")
theme_override_styles/hover = SubResource("StyleBoxEmpty_w1fex")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_2lrnb")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_gi81g")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_sar08")
theme_override_styles/normal = SubResource("StyleBoxEmpty_uy1vm")
